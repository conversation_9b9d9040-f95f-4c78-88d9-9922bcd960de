"""
测试修复后的景点处理系统
专门测试第17行记录的问题
"""

import re

def format_output(scenery_final: str) -> bool:
    """
    目的：判断 combine_scenry() 函数的输出格式是否符合要求。"景点1|景点2|景点3"，优化后更宽松
    输入：待判断格式的str
    输出：True or False
    """
    if not scenery_final or scenery_final.strip() == "":
        return True  # 空字符串也是合法的

    # 去除首尾空白字符和可能的首尾"|"
    cleaned = scenery_final.strip().strip('|')

    if not cleaned:
        return True  # 清理后为空也是合法的

    # 检查是否没有连续的"|"
    if '||' in scenery_final:
        return False

    # 分割景点并检查每个景点
    spots = [spot.strip() for spot in cleaned.split('|') if spot.strip()]

    if not spots:
        return True  # 没有有效景点也是合法的

    # 检查每个景点名称
    for spot in spots:
        # 允许中文、数字、常见标点符号，包括+号等
        # 特殊处理：允许一些英文字母（如798艺术区、K11等）和特殊符号
        if not re.match(r'^[\u4e00-\u9fff\d\w（）()·\-\s\+]+$', spot):
            return False

        # 景点名称不能太短或太长
        if len(spot) < 1 or len(spot) > 25:  # 稍微放宽长度限制
            return False

    return True


def simple_fallback_combine(all_spots, index):
    """
    备用景点合并方案，当LLM处理失败时使用
    进行简单的去重和基本清理
    """
    if not all_spots:
        return ""

    # 简单去重，保持顺序
    seen = set()
    unique_spots = []
    for spot in all_spots:
        spot = spot.strip()
        if spot and spot not in seen:
            seen.add(spot)
            unique_spots.append(spot)

    # 基本的非景点过滤
    filtered_spots = []
    exclude_keywords = ['酒店', '宾馆', '餐厅', '餐馆', '食府', '酒家', '商场', '购物中心', '机场', '火车站', '汽车站']

    for spot in unique_spots:
        # 检查是否包含明显的非景点关键词
        is_excluded = False
        for keyword in exclude_keywords:
            if keyword in spot:
                is_excluded = True
                break

        if not is_excluded:
            filtered_spots.append(spot)

    result = '|'.join(filtered_spots)
    print(f"第{index + 1}行记录：备用处理完成，结果：{result}")
    return result

def test_format_output():
    """测试格式验证函数"""
    print("=== 测试格式验证函数 ===")
    
    # 测试包含+号的景点名称
    test_cases = [
        "阳江|佛山|广州|马尾岛|海陵岛|武术表演+黄飞鸿纪念馆|广州塔小蛮腰",
        "故宫|天安门|王府井",
        "798艺术区|K11购物中心",
        "",
        "景点1|景点2||景点3",  # 连续分隔符
        "很长很长很长很长很长很长很长很长很长很长的景点名称"  # 超长名称
    ]
    
    for case in test_cases:
        result = format_output(case)
        print(f"输入: {case}")
        print(f"验证结果: {result}")
        print("-" * 50)

def test_fallback_combine():
    """测试备用合并函数"""
    print("\n=== 测试备用合并函数 ===")
    
    test_spots = [
        "阳江", "佛山", "广州", "马尾岛", "海陵岛", 
        "阳江北洛秘境度假酒店", "华盖路步行街", "岭南天地", 
        "黄飞鸿纪念馆", "十里银滩", "红树林国家湿地公园",
        "广州花园酒店", "点都德茶餐厅"
    ]
    
    result = simple_fallback_combine(test_spots, 17)
    print(f"输入景点: {test_spots}")
    print(f"备用处理结果: {result}")

if __name__ == "__main__":
    test_format_output()
    test_fallback_combine()
