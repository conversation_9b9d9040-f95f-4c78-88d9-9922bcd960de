"""
优化版景点提取和整合系统 (Copy 2 - Optimized)

主要优化改进：
1. 文本预处理优化：
   - 增加地名识别得分机制，提高景点相关文本的识别准确性
   - 扩展景点关键词库，包含更多相关词汇
   - 改进文本分割和评分算法，优先保留高价值内容

2. 语义去重和标准化：
   - 扩展景点名称标准化映射表，覆盖更多常见景点
   - 集成LLM智能景点名称标准化，自动识别和标准化未知景点
   - 改进去重逻辑，保持顺序的同时进行语义去重
   - 双重保障：手动映射表 + LLM智能处理，提高覆盖率和准确性

3. 合并策略调整：
   - 移除智能合并功能（避免依赖自定义词表）
   - 仅使用LLM进行景点合并，确保一致性
   - 增加单源处理逻辑，减少不必要的LLM调用

4. 性能和监控优化：
   - 详细的统计信息跟踪，包括不同合并方法的使用情况
   - 改进的日志记录和错误处理
   - 增加成功率和提取率计算

5. 提示词工程优化：
   - 更精确的景点识别提示词
   - 改进的合并提示词，包含更多标准化规则
   - 增加具体的示例和约束

处理流程：
1. 逐行从游记正文中读取信息：scenery、content
2. 智能预处理 content，提取景点相关内容
3. 将预处理后的文本传入 extract_scenery() 抽取景点
4. 使用LLM合并原始景点和提取景点，去重、规范格式
5. 格式验证，不满足则重试（最大3次）
6. 将结果写入 scenery_final 列，记录合并方法

优化目标：
- 优先级1：提高景点识别和合并准确性
- 优先级2：减少LLM模型调用次数
- 优先级3：缩短处理时间
"""

import os
import pandas as pd
import time
import logging
from logging.handlers import RotatingFileHandler
from pypinyin import lazy_pinyin
from tqdm import tqdm
import re
from datetime import datetime

from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv())

from zhipuai import ZhipuAI

client = ZhipuAI(api_key=os.environ["ZHIPUAI_API_KEY"])

# 配置日志系统
def setup_logger():
    """
    配置日志系统，输出到文件和控制台
    """
    # 创建logs目录
    if not os.path.exists('scenery/logs'):
        os.makedirs('scenery/logs')
    
    # 创建logger
    logger = logging.getLogger('scenery_processor')
    logger.setLevel(logging.INFO)
    
    # 避免重复添加handler
    if logger.handlers:
        logger.handlers.clear()
    
    # 使用固定的日志文件名，支持追加模式
    log_file = 'scenery/logs/scenery_process.log'
    
    # 创建文件handler，使用追加模式
    file_handler = RotatingFileHandler(log_file, maxBytes=1024*1024, backupCount=3, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建formatter，包含更详细的信息
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加handler到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 在每次启动时记录分隔符，便于区分不同的运行会话
    separator = "=" * 80
    startup_msg = f"程序启动 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    logger.info(separator)
    logger.info(startup_msg)
    logger.info(separator)
    
    return logger

# 初始化logger
logger = setup_logger()

def gen_glm_params(prompt):
    '''
    构造 GLM 模型请求参数 messages

    请求参数：
        prompt: 对应的用户提示词
    '''
    messages = [{"role": "user", "content": prompt}]
    return messages


def get_completion(prompt, model="glm-4-flash-250414", temperature=0.1, max_retries=3, row_index=None):
    '''
    获取 GLM 模型调用结果

    请求参数：
        prompt: 对应的提示词
        model: 调用的模型，默认为 glm-4-flash-250414，也可以选择 glm-4-plus，需要注意 api-key
        temperature: 模型输出的温度系数，控制输出的随机程度，取值范围是 0.0-1.0。温度系数越低，输出内容越一致。
        max_retries: 最大重试次数
        row_index: 当前处理的行索引，用于日志输出
    '''

    messages = gen_glm_params(prompt)
    
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature
            )
            if len(response.choices) > 0:
                return response.choices[0].message.content
            else:
                if row_index is not None:
                    logger.warning(f"第{row_index + 1}行记录：模型返回空结果，第 {attempt + 1} 次重试")
                else:
                    logger.warning(f"模型返回空结果，第 {attempt + 1} 次重试")
                
        except Exception as e:
            if row_index is not None:
                logger.error(f"第{row_index + 1}行记录：API调用失败 (第 {attempt + 1} 次): {e}")
            else:
                logger.error(f"API调用失败 (第 {attempt + 1} 次): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            
    return "generate answer error"

# get_completion("你好！")


def preprocess_text(content: str) -> str:
    """
    智能预处理文本，提取与景点相关的关键信息
    优化：增加地名识别得分，扩展关键词库，改进评分算法，增加负面关键词过滤
    """
    if pd.isna(content) or content.strip() == "":
        return ""

    # 扩展的景点相关关键词
    scenic_keywords = [
        '景区', '景点', '公园', '寺庙', '博物馆', '纪念馆', '广场', '大街', '古镇', '山', '湖', '海', '岛',
        '塔', '桥', '宫', '庙', '院', '楼', '阁', '亭', '园', '城', '关', '陵', '墓', '村', '镇',
        '游览', '参观', '游玩', '打卡', '到达', '抵达', '前往', '来到', '风景区', '旅游区', '名胜',
        '遗址', '故居', '文化', '历史', '古迹', '建筑', '雕塑', '石窟', '洞穴', '瀑布', '温泉'
    ]

    scenic_keywords += list(set([
        '森林', '沙漠', '草原', '峡谷', '山谷', '山脉', '溶洞', '海滩', '沙滩', '湖泊', '泉水', '冰川',
        '火山', '热带雨林', '湿地', '梯田', '溪流', '溪谷', '河流', '湖水', '海湾', '滩涂',
        '宫殿', '城堡', '祠堂', '关隘', '烽火台', '战场', '兵马俑', '古庙', '佛塔', '钟楼', '鼓楼',
        '古街', '老街', '民居', '古建筑', '遗产地', '壁画', '古城墙', '文庙', '书院',
        '游', '逛', '欣赏', '探访', '体验', '漫步', '踏青', '远眺', '驻足', '行走', '登山', '下山',
        '穿越', '合影', '游记', '旅拍', '旅行', '跋涉', '拍照',
        '景致', '胜景', '景观', '美景', '奇观', '风光', '名景', '地标', '胜地', '热门地', '旅游地',
        '游客中心', '观景台', '索道', '缆车', '步道', '栈道', '台阶', '山门'
    ]))

    # 负面关键词 - 明确的非景点标识
    negative_keywords = [
        '酒店', '宾馆', '旅馆', '民宿', '饭店', '餐厅', '餐馆', '食府', '酒家',
        '咖啡厅', '奶茶店', '甜品店', '小吃店', '火锅店', '烧烤店', '面馆', '粥店', '包子店', '饺子馆',
        '商场', '购物中心', '超市', '便利店', '专卖店', '旗舰店', '门店', '商店', '店铺', '市场',
        '机场', '火车站', '汽车站', '地铁站', '公交站', '码头', '港口', '停车场', '加油站',
        '医院', '诊所', '银行', '邮局', '学校', '大学', '中学', '小学', '幼儿园', '培训机构',
        '公司', '企业', '工厂', '办公楼', '写字楼', '商务中心', '会议中心', '展览中心',
        '画展', '展览', '艺术空间', '工作室', '私人收藏', '临时展', '个人展', '商业展',
        '住宿', '入住', '退房', '预订', '订餐', '点菜', '结账', '买单', '消费', '购买', '购物'
    ]

    # 按句子分割
    sentences = re.split(r'[。！？；\n]', content)

    # 计算每个句子的景点相关性得分
    scored_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if len(sentence) < 5:
            continue

        # 检查负面关键词，如果包含过多负面关键词则降低得分
        negative_score = sum(1 for keyword in negative_keywords if keyword in sentence)

        # 如果负面关键词过多，跳过这个句子
        if negative_score >= 2:
            continue

        # 计算景点关键词得分
        keyword_score = sum(1 for keyword in scenic_keywords if keyword in sentence)

        # 计算地名得分（识别可能的地名模式）
        location_score = len(re.findall(r'[\u4e00-\u9fff]{2,}(?:市|省|区|县|镇|村|山|湖|河|海|岛|园|寺|庙|宫|塔|桥|楼|阁)', sentence))

        # 综合得分：地名得分权重更高，负面关键词降低得分
        total_score = keyword_score + location_score * 2 - negative_score

        # 只保留得分为正的句子
        if total_score > 0:
            scored_sentences.append((sentence, total_score))

    # 按得分排序，优先保留高分句子
    scored_sentences.sort(key=lambda x: x[1], reverse=True)

    # 重新组合文本，确保不超过4000字符
    result_text = ""
    for sentence, score in scored_sentences:
        if len(result_text + sentence) < 1500:
            result_text += sentence + "。"
        else:
            break

    return result_text if result_text else content[:2500]


def extract_scenery(content: str, row_index: int = None) -> str:
    """
    输入：待提取景点信息的文本内容
    输出：文本中的景点，str格式
    """
    if pd.isna(content) or content.strip() == "":
        return ""

    # 预处理文本
    processed_content = preprocess_text(content)

    prompt = f"""
    # Role: 专业旅游景点识别专家

    ## Goals
    - 从旅行游记中精确识别和提取真实存在的旅游景点名称

    ## 严格约束
    - 仅提取文本中明确提到的景点，绝对不能创造、推测或添加任何未明确提及的景点
    - 输出格式：严格按照"景点1|景点2|景点3"格式，不包含任何解释、标点或其他文字
    - 景点名称标准化：使用常见简称，如"故宫"而非"故宫博物院"，"天安门"而非"天安门广场"
    - 中文限制：景点名称不能包含英文字母（除非是景点正式名称的一部分，如"798艺术区"）
    - 空值处理：如果没有识别到任何景点，返回空字符串

    ## 识别标准
    - 优先识别：著名景区、公园、寺庙、博物馆、纪念馆、历史建筑、自然景观
    - 包含：山、湖、海、岛、古镇、著名广场、著名大街、风景名胜区
    - 必须是对公众开放的旅游景点或自然景观

    ## 严格排除以下非景点
    ### 住宿类
    - 酒店、宾馆、旅馆、客栈、民宿、度假村（如：广州花园酒店、白天鹅宾馆、企鹅酒店）

    ### 餐饮类
    - 餐厅、餐馆、酒家、食府、茶馆、咖啡厅、小吃店、火锅店（如：点都德、陶陶居酒家、皮蛋弟砂锅店、载阳茶馆）
    - 任何带有"店"、"馆"、"厅"、"坊"等餐饮标识的场所

    ### 商业类
    - 商场、购物中心、超市、专卖店、商店（如：中华广场、正佳广场、许留山）
    - 任何以商业经营为主要目的的场所

    ### 交通类
    - 机场、火车站、汽车站、地铁站、码头、港口（如：白云国际机场、广州东站、潮汕站）

    ### 临时展览类
    - 画展、临时展览、艺术空间、工作室（如：陈丹青的画展、深圳babycity1618艺术空间）

    ### 企业机构类
    - 公司、企业、学校、医院、银行（如：腾讯、深圳大学高尔夫球场）

    ### 普通街道和城市名
    - 普通街道、商业区、城市名称（除非是著名旅游景点）

    ## 标准化规则
    - "故宫博物院" → "故宫"
    - "天安门广场" → "天安门"
    - "王府井大街" → "王府井"
    - "颐和园景区" → "颐和园"
    - "天坛公园" → "天坛"
    - "八达岭长城" → "长城"

    ## 示例
    输入："今天去了天安门广场，然后参观了故宫博物院，最后在王府井大街购物，晚上住在北京饭店"
    输出：天安门|故宫|王府井

    输入："从成都出发，先到了都江堰景区，下午去了青城山，晚上在当地餐厅吃饭"
    输出：都江堰|青城山

    输入："在深圳看了陈丹青的画展，去了深圳babycity1618艺术空间，住在某某酒店"
    输出：

    输入："在广州住了三天，主要是购物和吃饭，去了点都德茶餐厅，没去什么特别的景点"
    输出：

    ## 待分析文本
    {processed_content}

    请严格按照要求提取景点名称，直接输出结果：
    """

    try:
        scenery = get_completion(prompt, model='glm-4-flash-250414', temperature=0.05, row_index=row_index)
        return scenery.strip()
    except Exception as e:
        if row_index is not None:
            logger.error(f"第{row_index + 1}行记录：景点提取失败: {e}")
        else:
            logger.error(f"景点提取失败: {e}")
        return ""


def format_output(scenery_final:str)->bool:
    """
    目的：判断 combine_scenry() 函数的输出格式是否符合要求。"景点1|景点2|景点3"，优化后更宽松
    输入：待判断格式的str
    输出：True or False
    """
    if not scenery_final or scenery_final.strip() == "":
        return True  # 空字符串也是合法的

    # 去除首尾空白字符和可能的首尾"|"
    cleaned = scenery_final.strip().strip('|')

    if not cleaned:
        return True  # 清理后为空也是合法的

    # 检查是否没有连续的"|"
    if '||' in scenery_final:
        return False

    # 分割景点并检查每个景点
    spots = [spot.strip() for spot in cleaned.split('|') if spot.strip()]

    if not spots:
        return True  # 没有有效景点也是合法的

    # 检查每个景点名称
    for spot in spots:
        # 允许中文、数字、常见标点符号
        # 特殊处理：允许一些英文字母（如798艺术区、K11等）
        if not re.match(r'^[\u4e00-\u9fff\d\w（）()·\-\s]+$', spot):
            return False

        # 景点名称不能太短或太长
        if len(spot) < 1 or len(spot) > 20:
            return False

    return True


def llm_standardize_scenery(scenery_list, row_index=None):
    """
    使用LLM智能标准化景点名称
    输入：景点名称列表
    输出：标准化后的景点名称列表
    """
    if not scenery_list:
        return []

    # # 如果只有一个景点且长度较短，可能不需要LLM处理
    # if len(scenery_list) == 1 and len(scenery_list[0]) <= 4:
    #     return scenery_list

    scenery_str = '|'.join(scenery_list)

    prompt = f"""
    # Role: 景点名称标准化专家

    ## Goals
    - 将景点名称标准化为最常用的简称形式
    - 识别并合并相同景点的不同表述

    ## 严格约束
    - 输出格式：严格按照"景点1|景点2|景点3"格式，不包含任何其他文字、解释或标点
    - 标准化规则：使用最常见的简称（如"故宫"而非"故宫博物院"）
    - 去重规则：相同景点只保留一个标准名称
    - 保持原意：不能改变景点的实际含义，只能简化名称
    - 中文限制：不包含英文字母（除非是景点正式名称的一部分，如"798艺术区"）
    - 空值处理：如果没有有效景点，返回空字符串

    ## 标准化示例
    - "故宫博物院" → "故宫"
    - "天安门广场" → "天安门"
    - "王府井大街" → "王府井"
    - "颐和园景区" → "颐和园"
    - "天坛公园" → "天坛"
    - "八达岭长城景区" → "长城"
    - "北京动物园" → "北京动物园"（保持原名）
    - "中山公园" → "中山公园"（保持原名）

    ## 去重示例
    - "故宫|故宫博物院" → "故宫"
    - "长城|八达岭长城|慕田峪长城" → "长城"
    - "天安门|天安门广场" → "天安门"

    ## 待处理景点
    {scenery_str}

    请严格按照要求标准化景点名称，直接输出结果：
    """

    try:
        result = get_completion(prompt, model='glm-4-flash-250414', temperature=0.0, row_index=row_index)
        result = result.strip()

        if result and '|' in result:
            return [s.strip() for s in result.split('|') if s.strip()]
        elif result:
            return [result]
        else:
            return scenery_list  # 如果LLM返回空，保持原列表

    except Exception as e:
        if row_index is not None:
            logger.warning(f"第{row_index + 1}行记录：LLM标准化失败，使用手动映射: {e}")
        else:
            logger.warning(f"LLM标准化失败，使用手动映射: {e}")
        return scenery_list  # 失败时返回原列表


def semantic_deduplication(scenery_list, row_index=None, use_llm=True):
    """
    简化的景点去重和标准化（现在主要依赖LLM处理）
    保留参数以兼容现有调用
    """
    if not scenery_list:
        return []

    # 简单去重并保持顺序
    seen = set()
    result = []
    for spot in scenery_list:
        spot = spot.strip()
        if spot not in seen and spot:
            seen.add(spot)
            result.append(spot)

    return result



def combine_scenery(index, scenery, extract_scenery_result, max_retries=5) -> tuple:
    """
    使用LLM合并原始景点数据和提取的景点数据
    优化：完全依赖LLM进行景点标准化，去掉备用处理
    输入：原始景点数据，提取的景点数据
    输出：(合并后的景点数据, 合并方法)
    """
    # 处理输入为空的情况
    scenery = scenery if pd.notna(scenery) and scenery.strip() else ""
    extract_scenery_result = extract_scenery_result if extract_scenery_result and extract_scenery_result.strip() else ""

    # 如果两个输入都为空，直接返回
    if not scenery and not extract_scenery_result:
        return "", "empty"

    # 合并所有景点数据，让LLM统一处理
    all_spots = []
    if scenery:
        all_spots.extend([s.strip() for s in scenery.split('|') if s.strip()])
    if extract_scenery_result:
        all_spots.extend([s.strip() for s in extract_scenery_result.split('|') if s.strip()])

    # 如果没有有效景点，返回空
    if not all_spots:
        return "", "empty"

    # 使用LLM进行统一的景点标准化和去重（改进的提示词）
    input_scenery = '|'.join(all_spots)
    prompt = f"""
你是一个专业的景点数据标准化专家。请对以下景点列表进行标准化处理。

## 输入景点列表
{input_scenery}

## 处理要求
1. **严格过滤非景点**：移除所有非旅游景点的内容
2. **去重**：移除重复或相似的景点（如"故宫"和"故宫博物院"只保留"故宫"）
3. **标准化**：将景点名称标准化为常用简称
4. **格式**：输出格式必须严格为"景点1|景点2|景点3"，不包含任何其他文字
5. **中文限制**：景点名称应为中文，不包含英文字母（除非是正式名称如"798艺术区"）
6. **保持原意**：不能添加输入中未提及的景点

## 必须排除的非景点类型
### 住宿类
- 酒店、宾馆、旅馆、客栈、民宿、度假村
- 示例：广州花园酒店、白天鹅宾馆、企鹅酒店、阳江北洛秘境度假酒店

### 餐饮类
- 餐厅、餐馆、酒家、食府、茶馆、咖啡厅、小吃店、火锅店、砂锅店
- 示例：点都德、陶陶居酒家、皮蛋弟砂锅店、载阳茶馆、官塘陈记鱼生、阿彬牛肉、银灯食府

### 商业类
- 商场、购物中心、超市、专卖店、商店、市场
- 示例：中华广场、正佳广场、许留山、民信老铺

### 交通类
- 机场、火车站、汽车站、地铁站、码头、港口
- 示例：白云国际机场、广州东站、潮汕站、香洲港

### 临时展览类
- 画展、临时展览、艺术空间、工作室、私人展览
- 示例：陈丹青的画展、深圳babycity1618艺术空间

### 企业机构类
- 公司、企业、学校、医院、银行、办公场所
- 示例：腾讯、深圳大学高尔夫球场

## 标准化规则
- 故宫博物院/故宫博物馆 → 故宫
- 天安门广场/天安门城楼 → 天安门
- 王府井大街/王府井步行街 → 王府井
- 颐和园景区 → 颐和园
- 天坛公园 → 天坛
- 八达岭长城/慕田峪长城/司马台长城 → 长城
- 北海公园 → 北海
- 景山公园 → 景山
- 圆明园遗址公园 → 圆明园
- 香山公园 → 香山

## 输出示例
输入：故宫博物院|天安门广场|王府井大街|故宫|北京饭店|点都德茶餐厅
输出：故宫|天安门|王府井

输入：颐和园景区|天坛公园|颐和园|广州花园酒店|陶陶居酒家
输出：颐和园|天坛

输入：陈丹青的画展|深圳babycity1618艺术空间|皮蛋弟砂锅店|腾讯公司
输出：

请直接输出标准化后的景点列表，不要包含任何解释：
"""

    tryNum = 0
    while tryNum < max_retries:
        try:
            scenery_final = get_completion(prompt, model='glm-4-flash-250414', temperature=0.1, row_index=index)
            scenery_final = scenery_final.strip()

            # 清理可能的多余字符
            if scenery_final.startswith('输出：'):
                scenery_final = scenery_final[3:].strip()
            if scenery_final.startswith('结果：'):
                scenery_final = scenery_final[3:].strip()

            if format_output(scenery_final):
                if tryNum > 0:
                    logger.info(f"第{index + 1}行记录：LLM标准化重试成功，最终结果：{scenery_final}")
                else:
                    logger.info(f"第{index + 1}行记录：LLM标准化成功，结果：{scenery_final}")
                return scenery_final, "llm"
            else:
                logger.warning(f"第{index + 1}行记录：LLM标准化格式验证失败，第{tryNum + 1}次重试。结果：{scenery_final}")
                tryNum += 1
        except Exception as e:
            logger.error(f"第{index + 1}行记录：LLM标准化失败，第{tryNum + 1}次重试：{e}")
            tryNum += 1

    # 如果LLM处理失败，记录错误并返回空结果
    logger.error(f"第{index + 1}行记录：LLM标准化达到最大重试次数仍失败，返回空结果")
    return "", "error"


def main(csv_path, output_path):
    """
    主函数，处理整个流程
    """
    logger.info(f"🚀 开始处理CSV文件: {csv_path}")

    try:
        # header_df = pd.read_csv(csv_path, encoding='utf-8-sig', nrows=1)
        # column_name = header_df.columns.tolist()

        # df = pd.read_csv(csv_path, encoding='utf-8-sig', skiprows=1498, names=column_name)
        df = pd.read_csv(csv_path, encoding='utf-8-sig', nrows=1000)
        logger.info(f"📊 成功读取数据，共 {len(df)} 行")
    except Exception as e:
        logger.error(f"❌ 读取CSV文件失败: {e}")
        return

    print(df.columns)
    print(df.head(2))

    # 检查必要的列是否存在
    required_columns = ['scenery', 'content']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"❌ 缺少必要的列: {missing_columns}")
        return

    df['scenery_final'] = ""
    df['scenery_tmp'] = ""
    df['combine_method'] = ""

    # 优化的统计变量
    stats = {
        'success_count': 0,
        'error_count': 0,
        'empty_extractions': 0,
        'successful_extractions': 0,
        'llm_combines': 0,
        'empty_results': 0,
        'llm_errors': 0
    }

    # 使用tqdm显示进度条
    for i in tqdm(range(len(df)), desc="处理景点数据", unit="行"):
        try:
            scenery = df.loc[i, 'scenery']
            content = df.loc[i, 'content'][:3000]

            # 记录开始处理
            logger.debug(f"开始处理第{i + 1}行记录")

            # 提取景点
            extract_scenery_result = extract_scenery(content, i)
            df.at[i, 'scenery_tmp'] = extract_scenery_result

            # 更新提取统计
            if extract_scenery_result and extract_scenery_result.strip():
                stats['successful_extractions'] += 1
            else:
                stats['empty_extractions'] += 1

            # 合并景点
            scenery_final, combine_method = combine_scenery(i, scenery, extract_scenery_result, max_retries=2)

            df.at[i, 'scenery_final'] = scenery_final
            df.at[i, 'combine_method'] = combine_method

            # 更新合并方法统计
            if combine_method == 'llm':
                stats['llm_combines'] += 1
            elif combine_method == 'empty':
                stats['empty_results'] += 1
            elif combine_method == 'error':
                stats['llm_errors'] += 1

            stats['success_count'] += 1

            # 记录处理结果
            logger.debug(f"第{i + 1}行处理完成：{scenery_final} (方法: {combine_method})")

            # 每处理100行输出一次状态
            if (i + 1) % 100 == 0:
                logger.info(f"✅ 已处理 {i + 1} 行，成功: {stats['success_count']}, 失败: {stats['error_count']}")
                logger.info(f"   提取成功: {stats['successful_extractions']}, LLM合并: {stats['llm_combines']}, 单源处理: {stats['single_source_combines']}")

        except Exception as e:
            logger.error(f"❌ 处理第 {i + 1} 行时出错: {e}")
            df.at[i, 'scenery_final'] = df.loc[i, 'scenery'] if pd.notna(df.loc[i, 'scenery']) else ""
            df.at[i, 'combine_method'] = "error"
            stats['error_count'] += 1

    # 保存结果
    try:
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        logger.info(f"💾 结果已保存到: {output_path}")

        # 输出详细统计信息
        logger.info("📈 处理完成！详细统计:")
        logger.info(f"  总处理行数: {len(df)}")
        logger.info(f"  成功处理: {stats['success_count']}")
        logger.info(f"  处理失败: {stats['error_count']}")
        logger.info(f"  成功提取景点: {stats['successful_extractions']}")
        logger.info(f"  空提取结果: {stats['empty_extractions']}")
        logger.info(f"  LLM标准化成功: {stats['llm_combines']}")
        logger.info(f"  LLM标准化失败: {stats['llm_errors']}")
        logger.info(f"  空结果: {stats['empty_results']}")

        # 显示一些样例结果
        logger.info("📋 处理结果样例:")
        sample_df = df[df['scenery_final'].notna() & (df['scenery_final'] != "")].head(5)
        for idx, row in sample_df.iterrows():
            logger.info(f"  行 {idx + 1}: {row['scenery_final']} (方法: {row['combine_method']})")

        # 计算成功率
        if len(df) > 0:
            success_rate = (stats['success_count'] / len(df)) * 100
            extraction_rate = (stats['successful_extractions'] / len(df)) * 100
            logger.info(f"📊 成功率: {success_rate:.1f}%, 提取成功率: {extraction_rate:.1f}%")

    except Exception as e:
        logger.error(f"❌ 保存文件失败: {e}")


if __name__ == "__main__":
    
    csv_path = r'D:\Code\PythonCode\scrape\scenery\data_filtered_fenci_hot_dedup.csv'
    final_csv = r'D:\Code\PythonCode\scrape\scenery\data_filtered_fenci_hot_scenery.csv'
    main(csv_path, final_csv)