```
# Scenery 景点提取系统

## 项目概述

这是一个基于大语言模型的智能景点提取和标准化系统，用于从旅游游记文本中自动识别、提取和标准化景点信息。

## 核心文件分析

### 1. 主要文件

#### `processScenery copy 2.py` - 核心处理引擎
- **功能**：优化版景点提取和整合系统
- **类型**：Python脚本，约608行代码
- **作用**：从游记文本中智能提取景点信息并进行标准化处理

#### `data_filtered_fenci_hot_dedup.csv` - 核心数据文件
- **功能**：包含旅游游记数据的CSV文件
- **结构**：15个字段，65行数据（含表头）
- **内容**：广东地区旅游游记，已经过分词、热度筛选和去重处理

### 2. 系统架构

```

数据输入 → 文本预处理 → LLM景点提取 → 景点合并去重 → 格式标准化 → 结果输出

```

#### 处理流程
1. **数据读取**：从CSV文件读取`scenery`和`processed_text`字段
2. **智能预处理**：使用景点关键词库对文本进行评分和筛选
3. **景点提取**：调用智谱AI GLM-4模型识别景点
4. **合并去重**：结合原始景点数据和提取结果，进行语义去重
5. **格式验证**：确保输出符合"景点1|景点2|景点3"格式
6. **结果保存**：写入`scenery_final`、`combine_method`、`scenery_tmp`列

### 3. 核心功能模块

#### 文本预处理 (`preprocess_text`)
- **景点关键词库**：包含景区、公园、寺庙、博物馆等相关词汇
- **地名识别**：识别可能的地名模式，提高识别准确性
- **智能评分**：按句子计算景点相关性得分，优先保留高价值内容
- **文本优化**：确保处理文本不超过4000字符

#### 景点提取 (`extract_scenery`)
- **LLM调用**：使用智谱AI GLM-4-flash模型
- **严格约束**：仅提取明确提到的景点，不创造或推测
- **标准化规则**：
- "故宫博物院" → "故宫"
  - "天安门广场" → "天安门"
  - "王府井大街" → "王府井"
- **格式要求**：严格按照"景点1|景点2|景点3"格式输出

#### 景点合并 (`combine_scenery`)
- **多策略处理**：
- 单源处理：仅一个输入有值时的简化处理
- LLM合并：使用大模型进行智能合并
- 备用处理：重试失败时的手动合并
- **语义去重**：使用标准化映射表去除重复景点
- **重试机制**：最大重试3次，确保格式正确

#### 格式验证 (`format_output`)
- **格式检查**：验证是否符合"景点1|景点2|景点3"格式
- **字符限制**：不允许包含英文字母（除特殊情况如"798艺术区"）
- **连续分隔符检查**：避免出现"||"等错误格式

### 4. 数据文件结构

#### 主要字段说明
| 字段名 | 说明 | 示例 |
|--------|------|------|
| title | 游记标题 | "食光者∣2020年10月广府四城八日行记" |
| scenery | 原始景点数据 | "广州\|佛山\|珠海\|珠江\|光孝寺" |
| processed_text | 预处理游记正文 | 用于景点提取的文本内容 |
| content | 完整游记内容 | 原始游记全文 |
| province | 省份信息 | "广东" |
| days | 旅行天数 | 8 |

#### 输出字段
- **scenery_final**：最终标准化的景点列表
- **scenery_tmp**：中间提取结果
- **combine_method**：合并方法标识（single_source/llm/fallback/empty/error）

### 5. 技术特点

#### 优化改进
1. **文本预处理优化**：增加地名识别得分机制，提高景点相关文本识别准确性
2. **扩展关键词库**：包含更多景点相关词汇
3. **语义去重标准化**：扩展景点名称标准化映射表
4. **性能监控**：详细的统计信息跟踪和错误处理
5. **提示词工程**：优化的景点识别和合并提示词

#### 依赖环境
```python
# 主要依赖
import pandas as pd
import logging
from zhipuai import ZhipuAI
from tqdm import tqdm
import re
from dotenv import load_dotenv
```

#### API配置

- **智谱AI API**：需要配置 `ZHIPUAI_API_KEY`环境变量
- **模型**：使用GLM-4-flash-250414模型
- **温度参数**：0.05（确保输出稳定性）

### 6. 使用方法

#### 基本运行

```python
# 配置文件路径
csv_path = 'data_filtered_fenci_hot_dedup.csv'
output_path = 'data_filtered_fenci_hot_scenery.csv'

# 运行主函数
main(csv_path, output_path)
```

#### 处理统计

系统会自动跟踪以下统计信息：

- 成功/失败处理数量
- 提取成功率和空提取数量
- 不同合并方法使用情况
- 处理时间和性能指标

### 7. 输出示例

#### 输入数据

```
scenery: "广州|佛山|珠海"
processed_text: "今天去了天安门广场，参观了故宫博物院..."
```

#### 输出结果

```
scenery_final: "广州|佛山|珠海|天安门|故宫"
combine_method: "llm"
scenery_tmp: "天安门|故宫"
```

### 8. 项目特色

- **智能化**：结合传统文本处理和现代大语言模型
- **标准化**：统一的景点名称格式和标准化规则
- **容错性**：多重重试机制和备用处理策略
- **可扩展性**：模块化设计，易于功能扩展
- **监控性**：详细的日志记录和统计信息

### 9. 注意事项

1. **API限制**：需要有效的智谱AI API密钥
2. **处理时间**：大量数据处理可能需要较长时间
3. **格式要求**：严格按照"景点1|景点2|景点3"格式输出
4. **中文限制**：景点名称不包含英文字母（特殊情况除外）

---

*本系统专为旅游数据处理和景点信息标准化而设计，适用于旅游推荐、数据分析等应用场景。*

```

```
